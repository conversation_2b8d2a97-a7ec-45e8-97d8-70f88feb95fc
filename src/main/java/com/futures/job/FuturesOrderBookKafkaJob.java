package com.futures.job;

import com.futures.function.OrderBookReconstructionFunction;
import com.futures.function.PnLCalculationFunction;
import com.futures.function.CombinationOrderSplitter;
import com.futures.pojo.*;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.shaded.jackson2.com.fasterxml.jackson.databind.ObjectMapper;

import java.time.Duration;
import java.util.*;
import java.util.Properties;
import java.util.Map;

/**
 * 基于Kafka数据源的期货订单簿重建作业
 * 支持只消费部分消息和debug日志输出
 */
public class FuturesOrderBookKafkaJob {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static void main(String[] args) throws Exception {
        System.out.println("启动期货全量订单簿重建系统(Kafka版)...");
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);

        // Kafka配置
        Properties props = new Properties();
        props.setProperty("bootstrap.servers", "localhost:9092"); // 修改为你的Kafka地址
        props.setProperty("group.id", "orderbook-debug-group");
        props.setProperty("auto.offset.reset", "earliest");
        props.setProperty("enable.auto.commit", "false");

        // 只消费部分消息（如每个topic只取前100条）
        int maxRecords = 100;

        // 单腿订单流 - 使用新的KafkaSource API，增加空闲检测
        KafkaSource<String> singleLegSource = KafkaSource.<String>builder()
                .setBootstrapServers("localhost:9092")
                .setTopics("single_leg_orders")
                .setGroupId("orderbook-debug-group")
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();

        DataStream<String> singleLegStringStream = env
                .fromSource(singleLegSource, 
                    WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofMillis(100))
                        .withIdleness(Duration.ofSeconds(5)) // 增加空闲检测，5秒无数据则推进水位线
                        .withTimestampAssigner((event, timestamp) -> {
                            // 解析时间戳用于watermark - 使用统一时区
                            try {
                                Map<String, Object> jsonMap = objectMapper.readValue(event, Map.class);
                                String ordDt = (String) jsonMap.get("ord_dt");
                                String ordTm = (String) jsonMap.get("ord_tm");
                                Object ordTmMillisecObj = jsonMap.get("ord_tm_millisec");
                                long ordTmMillisec = ordTmMillisecObj != null ? ((Number) ordTmMillisecObj).longValue() : 0L;
                                return FuturesOrderBookJob.parseTimestampWithMillisec(ordDt, ordTm, ordTmMillisec);
                            } catch (Exception e) {
                                return System.currentTimeMillis();
                            }
                        }), "Kafka-单腿订单")
                .process(new LimitProcessFunction(maxRecords, "[Kafka-SingleLeg]"));

        // 组合订单流
        KafkaSource<String> combSource = KafkaSource.<String>builder()
                .setBootstrapServers("localhost:9092")
                .setTopics("combination_orders")
                .setGroupId("orderbook-debug-group")
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();

        DataStream<String> combStringStream = env
                .fromSource(combSource, 
                    WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofMillis(100))
                        .withIdleness(Duration.ofSeconds(5)) // 增加空闲检测
                        .withTimestampAssigner((event, timestamp) -> {
                            try {
                                Map<String, Object> jsonMap = objectMapper.readValue(event, Map.class);
                                String ordDt = (String) jsonMap.get("ord_dt");
                                String ordTm = (String) jsonMap.get("ord_tm");
                                Object ordTmMillisecObj = jsonMap.get("ord_tm_millisec");
                                long ordTmMillisec = ordTmMillisecObj != null ? ((Number) ordTmMillisecObj).longValue() : 0L;
                                return FuturesOrderBookJob.parseTimestampWithMillisec(ordDt, ordTm, ordTmMillisec);
                            } catch (Exception e) {
                                return System.currentTimeMillis();
                            }
                        }), "Kafka-组合订单")
                .process(new LimitProcessFunction(maxRecords, "[Kafka-Comb]"));

        // 交易流
        KafkaSource<String> tradeSource = KafkaSource.<String>builder()
                .setBootstrapServers("localhost:9092")
                .setTopics("trades")
                .setGroupId("orderbook-debug-group")
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();

        DataStream<String> tradeStringStream = env
                .fromSource(tradeSource, 
                    WatermarkStrategy.<String>forBoundedOutOfOrderness(Duration.ofMillis(100))
                        .withIdleness(Duration.ofSeconds(5)) // 增加空闲检测
                        .withTimestampAssigner((event, timestamp) -> {
                            try {
                                Map<String, Object> jsonMap = objectMapper.readValue(event, Map.class);
                                String trdDt = (String) jsonMap.get("trd_dt");
                                String trdTm = (String) jsonMap.get("trd_tm");
                                Object trdTmMillisecObj = jsonMap.get("trd_tm_millisec");
                                long trdTmMillisec = trdTmMillisecObj != null ? ((Number) trdTmMillisecObj).longValue() : 0L;
                                return FuturesOrderBookJob.parseTimestampWithMillisec(trdDt, trdTm, trdTmMillisec);
                            } catch (Exception e) {
                                return System.currentTimeMillis();
                            }
                        }), "Kafka-交易")
                .process(new LimitProcessFunction(maxRecords, "[Kafka-Trade]"));

        // 解析与处理
        DataStream<SingleLegOrderEvent> singleLegStream = singleLegStringStream
                .map(new DebugSingleLegOrderParser())
                .filter(order -> order != null)
                .name("解析单腿订单");

        DataStream<CombinationOrderEvent> combStream = combStringStream
                .map(new DebugCombinationOrderParser())
                .filter(order -> order != null)
                .name("解析组合订单");

        DataStream<TradeEvent> tradeStream = tradeStringStream
                .map(new DebugTradeEventParser())
                .filter(trade -> trade != null)
                .name("解析交易事件");

        // 拆分组合订单
        SingleOutputStreamOperator<Object> combOrderSplitterStream = combStream
                .process(new CombinationOrderSplitter())
                .name("拆分组合订单");
        DataStream<CombinationOrderSplitter.CombinationOrderLegInfo> leg1Stream =
                combOrderSplitterStream.getSideOutput(CombinationOrderSplitter.LEG1_OUTPUT_TAG);
        DataStream<CombinationOrderSplitter.CombinationOrderLegInfo> leg2Stream =
                combOrderSplitterStream.getSideOutput(CombinationOrderSplitter.LEG2_OUTPUT_TAG);

        // 合并所有订单事件
        DataStream<Object> allOrderEvents = singleLegStream.<Object>map(order -> order)
                .union(leg1Stream.map(leg -> (Object) leg))
                .union(leg2Stream.map(leg -> (Object) leg));

        // 订单簿重建
        DataStream<OrderBookSnapshot> orderBookStream = allOrderEvents
                .keyBy(FuturesOrderBookKafkaJob::extractContractCode)
                .process(new OrderBookReconstructionFunction())
                .name("订单簿重建");

        // PnL计算
        SingleOutputStreamOperator<PnLResult> pnlStream = tradeStream
                .keyBy(TradeEvent::getSettle_memb_memb_cde)
                .process(new PnLCalculationFunction())
                .name("PnL计算");
                
        // 获取异常交易和持仓盈亏侧输出流
        DataStream<TradeEvent> anomalyTradeStream = pnlStream.getSideOutput(PnLCalculationFunction.ANOMALY_TRADE_OUTPUT_TAG);
        DataStream<PnLResult> mtmPnlStream = pnlStream.getSideOutput(PnLCalculationFunction.MTM_PNL_OUTPUT_TAG);

        // 全局快照聚合和输出
        DataStream<String> globalSnapshotStream = orderBookStream
                .keyBy(snapshot -> "GLOBAL_KEY")
                .process(new FuturesOrderBookJob.GlobalSnapshotAggregator())
                .setParallelism(1)
                .name("全局快照聚合器");

        // 输出
        globalSnapshotStream.print("全量订单簿");
        pnlStream.map(FuturesOrderBookKafkaJob::formatPnLResult).print("PnL结果");
        
        // 输出异常交易和持仓盈亏监控
        anomalyTradeStream.map(trade -> String.format("[Kafka-异常交易] 合约:%s, 交易号:%s, 方向:%s, 数量:%d - 无对应持仓",
            trade.getContract_cde(), trade.getTrd_nbr(), trade.getB_s_tag(), trade.getTrd_vol()))
            .print("Kafka-异常交易监控");
            
        mtmPnlStream.map(pnl -> String.format("[Kafka-持仓盈亏] 会员:%s, 日期:%s, 盈亏:%.2f",
            pnl.getSettle_memb_memb_cde(), pnl.getDate(), pnl.getTotal_pnl()))
            .print("Kafka-持仓盈亏");

        System.out.println("开始执行Flink作业(Kafka版)...");
        env.execute("期货全量订单簿重建系统-Kafka");
    }

    // 只取部分消息的ProcessFunction
    public static class LimitProcessFunction extends ProcessFunction<String, String> {
        private final int maxCount;
        private final String debugPrefix;
        private int count = 0;
        public LimitProcessFunction(int maxCount, String debugPrefix) {
            this.maxCount = maxCount;
            this.debugPrefix = debugPrefix;
        }
        @Override
        public void processElement(String value, Context ctx, Collector<String> out) throws Exception {
            if (count < maxCount) {
                System.out.println(debugPrefix + " " + value);
                out.collect(value);
                count++;
            }
        }
    }

    // Debug解析器
    private static class DebugSingleLegOrderParser implements MapFunction<String, SingleLegOrderEvent> {
        @Override
        public SingleLegOrderEvent map(String jsonStr) throws Exception {
            SingleLegOrderEvent order = FuturesOrderBookJob.parseSingleLegOrder(jsonStr);
            if (order != null) {
                System.out.println("[Debug-SingleLegOrder] " + order.getContract_cde() + " " + order.getOrd_nbr() + " 状态:" + order.getOrd_sts() + " 剩余:" + order.getRmn_vol());
            }
            return order;
        }
    }
    private static class DebugCombinationOrderParser implements MapFunction<String, CombinationOrderEvent> {
        @Override
        public CombinationOrderEvent map(String jsonStr) throws Exception {
            CombinationOrderEvent order = FuturesOrderBookJob.parseCombinationOrder(jsonStr);
            if (order != null) {
                System.out.println("[Debug-CombOrder] " + order.getContract_cde() + " " + order.getOrd_nbr() + " 状态:" + order.getOrd_sts() + " 剩余:" + order.getRmn_vol());
            }
            return order;
        }
    }
    private static class DebugTradeEventParser implements MapFunction<String, TradeEvent> {
        @Override
        public TradeEvent map(String jsonStr) throws Exception {
            TradeEvent trade = FuturesOrderBookJob.parseTradeEvent(jsonStr);
            if (trade != null) {
                System.out.println("[Debug-Trade] " + trade.getContract_cde() + " " + trade.getOrd_nbr() + " 成交量:" + trade.getTrd_vol());
            }
            return trade;
        }
    }

    private static String extractContractCode(Object order) {
        if (order instanceof SingleLegOrderEvent) {
            return ((SingleLegOrderEvent) order).getContract_cde();
        } else if (order instanceof CombinationOrderSplitter.CombinationOrderLegInfo) {
            return ((CombinationOrderSplitter.CombinationOrderLegInfo) order).getCurrentLegContract();
        }
        return "UNKNOWN_CONTRACT";
    }
    private static String formatPnLResult(PnLResult pnl) {
        return String.format("[PnL] 会员:%s PnL:%.2f 日期:%s",
                pnl.getSettle_memb_memb_cde(),
                pnl.getTotal_pnl(),
                pnl.getDate());
    }
}
