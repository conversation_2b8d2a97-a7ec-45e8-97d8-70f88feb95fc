package com.futures.function;

import com.futures.pojo.CombinationOrderEvent;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.io.Serializable;

/**
 * 组合订单分发器 - 将组合订单分发到两条腿的处理流中
 * 解决原有架构中只处理第一条腿的问题
 */
public class CombinationOrderSplitter extends ProcessFunction<CombinationOrderEvent, Object> {
    
    // 第一条腿输出标签
    public static final OutputTag<CombinationOrderLegInfo> LEG1_OUTPUT_TAG = 
        new OutputTag<CombinationOrderLegInfo>("leg1-output"){};
    
    // 第二条腿输出标签  
    public static final OutputTag<CombinationOrderLegInfo> LEG2_OUTPUT_TAG = 
        new OutputTag<CombinationOrderLegInfo>("leg2-output"){};
    
    @Override
    public void processElement(CombinationOrderEvent combOrder, Context ctx, Collector<Object> out) throws Exception {

        // 解析组合合约代码，提取腿信息
        String combinationContract = combOrder.getContract_cde();
        String leg1Contract = combOrder.getLeg_1_contract_cde();
        String leg2Contract = combOrder.getLeg_2_contract_cde();

        // 调试日志
        System.out.println(String.format("[CombOrder Debug] Processing combination order: OrderNum=%s, CombContract=%s, Leg1=%s, Leg2=%s, Status=%s, RemainingVol=%d, TradedVol=%d",
            combOrder.getOrd_nbr(), combinationContract, leg1Contract, leg2Contract,
            combOrder.getOrd_sts(), combOrder.getRmn_vol(), combOrder.getTrd_vol()));

        // 为第一条腿创建信息
        CombinationOrderLegInfo leg1Info = new CombinationOrderLegInfo(
            combOrder.getOrd_nbr(),
            leg1Contract, // 当前腿合约
            leg2Contract, // 对手腿合约
            combOrder.getB_s_tag(),
            combOrder.getOrd_prc(),
            combOrder.getRmn_vol(),
            combOrder.getOrd_sts(),
            combOrder.getEventTimestamp(),
            1 // 腿序号
        );

        // 为第二条腿创建信息（买卖方向相反）
        String oppositeBuySellTag = "B".equals(combOrder.getB_s_tag()) ? "S" : "B";
        CombinationOrderLegInfo leg2Info = new CombinationOrderLegInfo(
            combOrder.getOrd_nbr(),
            leg2Contract, // 当前腿合约
            leg1Contract, // 对手腿合约
            oppositeBuySellTag, // 相反方向
            -combOrder.getOrd_prc(), // 价差取负值
            combOrder.getRmn_vol(),
            combOrder.getOrd_sts(),
            combOrder.getEventTimestamp(),
            2 // 腿序号
        );

        // 分发到对应的侧输出流
        ctx.output(LEG1_OUTPUT_TAG, leg1Info);
        ctx.output(LEG2_OUTPUT_TAG, leg2Info);
    }
    
    /**
     * 组合订单腿信息 - 包含处理单条腿所需的所有信息
     */
    public static class CombinationOrderLegInfo implements Serializable {
        private static final long serialVersionUID = 1L;
        
        private String ordNbr;
        private String currentLegContract; // 当前腿合约代码
        private String oppositeLegContract; // 对手腿合约代码
        private String buySellTag;
        private double spreadPrice;
        private long remainingVol;
        private String orderStatus;
        private long eventTimestamp;
        private int legNumber; // 腿序号 1或2
        
        public CombinationOrderLegInfo() {}
        
        public CombinationOrderLegInfo(String ordNbr, String currentLegContract, String oppositeLegContract,
                                     String buySellTag, double spreadPrice, long remainingVol, 
                                     String orderStatus, long eventTimestamp, int legNumber) {
            this.ordNbr = ordNbr;
            this.currentLegContract = currentLegContract;
            this.oppositeLegContract = oppositeLegContract;
            this.buySellTag = buySellTag;
            this.spreadPrice = spreadPrice;
            this.remainingVol = remainingVol;
            this.orderStatus = orderStatus;
            this.eventTimestamp = eventTimestamp;
            this.legNumber = legNumber;
        }
        
        // Getters and setters
        public String getOrdNbr() { return ordNbr; }
        public void setOrdNbr(String ordNbr) { this.ordNbr = ordNbr; }
        
        public String getCurrentLegContract() { return currentLegContract; }
        public void setCurrentLegContract(String currentLegContract) { this.currentLegContract = currentLegContract; }
        
        public String getOppositeLegContract() { return oppositeLegContract; }
        public void setOppositeLegContract(String oppositeLegContract) { this.oppositeLegContract = oppositeLegContract; }
        
        public String getBuySellTag() { return buySellTag; }
        public void setBuySellTag(String buySellTag) { this.buySellTag = buySellTag; }
        
        public double getSpreadPrice() { return spreadPrice; }
        public void setSpreadPrice(double spreadPrice) { this.spreadPrice = spreadPrice; }
        
        public long getRemainingVol() { return remainingVol; }
        public void setRemainingVol(long remainingVol) { this.remainingVol = remainingVol; }
        
        public String getOrderStatus() { return orderStatus; }
        public void setOrderStatus(String orderStatus) { this.orderStatus = orderStatus; }
        
        public long getEventTimestamp() { return eventTimestamp; }
        public void setEventTimestamp(long eventTimestamp) { this.eventTimestamp = eventTimestamp; }
        
        public int getLegNumber() { return legNumber; }
        public void setLegNumber(int legNumber) { this.legNumber = legNumber; }
    }
}
