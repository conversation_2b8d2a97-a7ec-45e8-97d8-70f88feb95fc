package com.futures.function;

import com.futures.pojo.OrderBookSnapshot;
import com.futures.pojo.SingleLegOrderEvent;
import com.futures.function.CombinationOrderSplitter.CombinationOrderLegInfo;
import org.apache.flink.api.common.state.*;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import java.util.*;

/**
 * 订单簿重建函数 - 支持单腿订单和组合订单腿信息处理
 */
public class OrderBookReconstructionFunction extends KeyedProcessFunction<String, Object, OrderBookSnapshot> {

    // 状态管理
    private transient MapState<String, SingleLegOrderEvent> activeSingleLegOrders;
    private transient MapState<String, CombinationOrderLegInfo> activeCombOrders;
    private transient ValueState<Double> bestBidPrice;
    private transient ValueState<Double> bestAskPrice;
    private transient ValueState<Boolean> timerRegistered;

    private static final long SNAPSHOT_INTERVAL_MS = 500;

    @Override
    public void open(Configuration parameters) throws Exception {
        StateTtlConfig ttlConfig = StateTtlConfig
                .newBuilder(Time.hours(24))
                .setUpdateType(StateTtlConfig.UpdateType.OnCreateAndWrite)
                .setStateVisibility(StateTtlConfig.StateVisibility.NeverReturnExpired)
                .build();

        // 初始化状态
        MapStateDescriptor<String, SingleLegOrderEvent> singleLegDescriptor =
                new MapStateDescriptor<>("active-single-leg-orders", String.class, SingleLegOrderEvent.class);
        singleLegDescriptor.enableTimeToLive(ttlConfig);
        activeSingleLegOrders = getRuntimeContext().getMapState(singleLegDescriptor);

        MapStateDescriptor<String, CombinationOrderLegInfo> combOrdersDescriptor =
                new MapStateDescriptor<>("active-comb-orders", String.class, CombinationOrderLegInfo.class);
        combOrdersDescriptor.enableTimeToLive(ttlConfig);
        activeCombOrders = getRuntimeContext().getMapState(combOrdersDescriptor);

        ValueStateDescriptor<Double> bestBidDescriptor =
                new ValueStateDescriptor<>("best-bid-price", Double.class);
        bestBidDescriptor.enableTimeToLive(ttlConfig);
        bestBidPrice = getRuntimeContext().getState(bestBidDescriptor);

        ValueStateDescriptor<Double> bestAskDescriptor =
                new ValueStateDescriptor<>("best-ask-price", Double.class);
        bestAskDescriptor.enableTimeToLive(ttlConfig);
        bestAskPrice = getRuntimeContext().getState(bestAskDescriptor);
        
        ValueStateDescriptor<Boolean> timerDescriptor =
                new ValueStateDescriptor<>("timer-registered", Boolean.class, false);
        timerRegistered = getRuntimeContext().getState(timerDescriptor);
    }

    @Override
    public void processElement(Object value, Context ctx, Collector<OrderBookSnapshot> out) throws Exception {
        System.out.println(String.format("[OrderBook Debug] 接收到事件: 类型=%s", value.getClass().getSimpleName()));
        
        try {
            if (value instanceof SingleLegOrderEvent) {
                System.out.println("[OrderBook Debug] 处理单腿订单事件");
                processSingleLegOrder((SingleLegOrderEvent) value);
            } else if (value instanceof CombinationOrderLegInfo) {
                System.out.println("[OrderBook Debug] 处理组合订单腿事件");
                processCombinationOrderLeg((CombinationOrderLegInfo) value);
            }

            // 注册定时器生成快照
            if (!Boolean.TRUE.equals(timerRegistered.value())) {
                long currentTime = ctx.timestamp();
                if (currentTime <= 0) {
                    currentTime = ctx.timerService().currentProcessingTime();
                }
                long nextSnapshotTime = ((currentTime / SNAPSHOT_INTERVAL_MS) + 1) * SNAPSHOT_INTERVAL_MS;
                ctx.timerService().registerEventTimeTimer(nextSnapshotTime);
                timerRegistered.update(true);
            }
        } catch (Exception e) {
            System.err.println("订单簿重建处理元素失败: " + value.toString());
            e.printStackTrace();
        }
    }

    /**
     * 处理单腿订单事件
     */
    private void processSingleLegOrder(SingleLegOrderEvent order) throws Exception {
        String orderStatusStr = order.getOrd_sts();
        
        System.out.println(String.format("[SingleLeg Debug] 处理单腿订单: 订单号=%s, 合约=%s, 状态=%s, 剩余量=%d", 
            order.getOrd_nbr(), order.getContract_cde(), orderStatusStr, order.getRmn_vol()));
        
        if (orderStatusStr == null || orderStatusStr.isEmpty() || "nan".equalsIgnoreCase(orderStatusStr)) {
            System.out.println("[SingleLeg Debug] 订单状态无效，跳过处理");
            return; // 跳过无效状态的订单
        }

        int orderStatus = (int) Double.parseDouble(orderStatusStr);
        String orderNumber = order.getOrd_nbr();

        // 状态 1 和 3 表示订单活跃
        if (orderStatus == 1 || orderStatus == 3) {
            if (order.getRmn_vol() > 0) {
                activeSingleLegOrders.put(orderNumber, order);
                System.out.println(String.format("[SingleLeg Debug] 添加活跃订单: %s, 剩余量=%d", orderNumber, order.getRmn_vol()));
            } else {
                activeSingleLegOrders.remove(orderNumber);
                System.out.println(String.format("[SingleLeg Debug] 移除完成订单: %s", orderNumber));
            }
        } else {
            // 其他状态表示订单不活跃
            activeSingleLegOrders.remove(orderNumber);
            System.out.println(String.format("[SingleLeg Debug] 移除非活跃订单: %s, 状态=%d", orderNumber, orderStatus));
        }
    }

    /**
     * 处理组合订单腿事件
     */
    private void processCombinationOrderLeg(CombinationOrderLegInfo legInfo) throws Exception {
        String orderStatus = legInfo.getOrderStatus();
        String orderKey = legInfo.getOrdNbr() + "_" + legInfo.getLegNumber();

        // 状态 1 和 3 表示订单活跃
        if ("1".equals(orderStatus) || "3".equals(orderStatus)) {
            if (legInfo.getRemainingVol() > 0) {
                activeCombOrders.put(orderKey, legInfo);
            } else {
                activeCombOrders.remove(orderKey);
            }
        } else {
            activeCombOrders.remove(orderKey);
        }
    }

    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<OrderBookSnapshot> out) throws Exception {
        String contractCode = ctx.getCurrentKey();
        OrderBookSnapshot snapshot = new OrderBookSnapshot(contractCode);
        snapshot.setTimestamp(timestamp);
        
        System.out.println(String.format("[OrderBook Debug] 合约 %s 生成快照，时间戳=%d", contractCode, timestamp));

        // 1. 构建基础层 (Base Layer)
        // 使用临时的TreeMap来按价格排序和聚合
        Map<Double, Long> bids = new TreeMap<>(Collections.reverseOrder()); // 买盘降序
        Map<Double, Long> asks = new TreeMap<>(); // 卖盘升序

        int singleLegCount = 0;
        for (SingleLegOrderEvent order : activeSingleLegOrders.values()) {
            singleLegCount++;
            System.out.println(String.format("[OrderBook Debug] 处理单腿订单: 订单号=%s, 合约=%s, 方向=%s, 价格=%.2f, 剩余量=%d", 
                order.getOrd_nbr(), order.getContract_cde(), order.getB_s_tag(), order.getOrd_prc(), order.getRmn_vol()));
            
            if ("B".equals(order.getB_s_tag())) {
                bids.merge(order.getOrd_prc(), order.getRmn_vol(), Long::sum);
            } else {
                asks.merge(order.getOrd_prc(), order.getRmn_vol(), Long::sum);
            }
        }
        
        System.out.println(String.format("[OrderBook Debug] 合约 %s 基础层: 处理了%d个单腿订单, 买盘%d档, 卖盘%d档", 
            contractCode, singleLegCount, bids.size(), asks.size()));
        
        snapshot.setBids(bids);
        snapshot.setAsks(asks);

        // 更新最优买卖价缓存，为虚拟层计算做准备
        updateBestPrices(bids, asks);

        // 2. 构建虚拟层 (Virtual Layer)
        int virtualOrdersAdded = 0;
        for (CombinationOrderLegInfo legInfo : activeCombOrders.values()) {
            if (legInfo.getRemainingVol() > 0) {
                addVirtualOrdersToSnapshot(snapshot, legInfo);
                virtualOrdersAdded++;
            }
        }
        
        if (virtualOrdersAdded > 0) {
            System.out.println(String.format("[OrderBook Info] 合约 %s 添加了 %d 个虚拟订单", contractCode, virtualOrdersAdded));
        }
        
        // 3. 输出快照 (仅当订单簿不为空时)
        if (!snapshot.getBids().isEmpty() || !snapshot.getAsks().isEmpty()) {
            System.out.println(String.format("[OrderBook Debug] 合约 %s 输出快照: 买盘=%d档, 卖盘=%d档", 
                contractCode, snapshot.getBids().size(), snapshot.getAsks().size()));
            out.collect(snapshot);
        } else {
            System.out.println(String.format("[OrderBook Debug] 合约 %s 订单簿为空，跳过快照输出", contractCode));
        }

        // 4. 检查是否需要继续注册定时器
        // 对于有界数据流，当水位线达到Long.MAX_VALUE时表示数据流结束，应停止注册新定时器
        long currentWatermark = ctx.timerService().currentWatermark();
        boolean isDataStreamEnded = currentWatermark >= Long.MAX_VALUE;
        
        System.out.println(String.format("[Timer Debug] 合约 %s - 水位线: %d, 数据流结束: %s", 
            ctx.getCurrentKey(), currentWatermark, isDataStreamEnded));
        
        if (isDataStreamEnded) {
            // 数据流已结束，停止注册新定时器
            timerRegistered.update(false);
            System.out.println(String.format("[Timer Debug] 合约 %s 数据流结束，停止定时器", ctx.getCurrentKey()));
        } else {
            // 数据流未结束，继续注册定时器以保持实时快照输出
            long nextTimerTime = timestamp + SNAPSHOT_INTERVAL_MS;
            ctx.timerService().registerEventTimeTimer(nextTimerTime);
            System.out.println(String.format("[Timer Debug] 合约 %s 注册下次定时器: %d (当前: %d)", 
                ctx.getCurrentKey(), nextTimerTime, timestamp));
        }
    }

    /**
     * 根据聚合后的买卖盘，更新缓存中的最优价格
     */
    private void updateBestPrices(Map<Double, Long> bids, Map<Double, Long> asks) throws Exception {
        Double bestBid = bids.isEmpty() ? null : bids.keySet().iterator().next();
        Double bestAsk = asks.isEmpty() ? null : asks.keySet().iterator().next();
        bestBidPrice.update(bestBid);
        bestAskPrice.update(bestAsk);
    }

        /**
     * 为订单簿快照添加虚拟订单
     */
    private void addVirtualOrdersToSnapshot(OrderBookSnapshot snapshot, CombinationOrderLegInfo legInfo) throws Exception {
        String buySellTag = legInfo.getBuySellTag();
        long volume = legInfo.getRemainingVol();
        double spreadPrice = legInfo.getSpreadPrice();

        // 获取当前合约的最优价格
        Double currentBestBid = bestBidPrice.value();
        Double currentBestAsk = bestAskPrice.value();

        System.out.println(String.format("[Virtual Order] 腿信息: 方向=%s, 数量=%d, 价差=%.2f", 
            buySellTag, volume, spreadPrice));

        // 改进的组合订单虚拟价格计算逻辑
        // 组合订单的价差应该体现两条腿之间的真实价差关系
        if ("B".equals(buySellTag)) { // 当前腿是买腿
            // 组合订单买腿意味着：当前合约买入，对手合约卖出
            // 虚拟卖单价格 = 基准价格 + 真实价差
            if (currentBestAsk != null) {
                // 使用当前最优卖价作为基准，加上价差作为虚拟卖单价格
                double virtualSellPrice = currentBestAsk + Math.abs(spreadPrice);
                snapshot.getAsks().merge(virtualSellPrice, volume, Long::sum);
                System.out.println(String.format("[Virtual Order Debug] 添加虚拟卖单: 价格=%.2f (基准=%.2f + 价差=%.2f), 数量=%d", 
                    virtualSellPrice, currentBestAsk, Math.abs(spreadPrice), volume));
            }
        } else { // 当前腿是卖腿
            // 组合订单卖腿意味着：当前合约卖出，对手合约买入
            // 虚拟买单价格 = 基准价格 - 真实价差
            if (currentBestBid != null) {
                // 使用当前最优买价作为基准，减去价差作为虚拟买单价格
                double virtualBuyPrice = currentBestBid - Math.abs(spreadPrice);
                snapshot.getBids().merge(virtualBuyPrice, volume, Long::sum);
                System.out.println(String.format("[Virtual Order Debug] 添加虚拟买单: 价格=%.2f (基准=%.2f - 价差=%.2f), 数量=%d", 
                    virtualBuyPrice, currentBestBid, Math.abs(spreadPrice), volume));
            }
        }
    }
}
