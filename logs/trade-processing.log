2025-08-20 14:33:38.091 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:43] - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 14:33:38.093 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:60] - 使用本地测试环境配置: localhost:9092
2025-08-20 14:33:38.133 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:89] - 应用本地测试Kafka配置
2025-08-20 14:33:38.188 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:255] - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 14:33:39.319 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:33:39.319 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:33:39.321 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:33:39.321 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:33:39.322 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:33:39.323 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:33:39.323 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:33:39.323 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:33:39.324 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:33:39.324 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:33:39.324 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:33:39.324 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:33:39.325 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:33:39.325 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:33:39.325 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:33:39.325 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:33:39.325 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:33:39.325 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:33:39.325 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:33:39.325 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:33:39.325 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:33:39.326 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:33:39.326 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:33:39.326 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:33:39.326 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:33:39.326 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:33:39.327 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:33:39.327 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:33:39.327 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:33:39.327 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:33:39.327 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:33:39.327 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:33:39.327 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:33:39.328 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:33:39.328 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 14:33:39.328 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:33:39.328 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 14:33:39.328 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 14:33:39.328 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:33:39.328 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 14:33:39.328 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 14:33:39.328 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:33:39.329 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 14:33:39.328 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 14:33:39.329 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:33:39.329 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 14:33:39.329 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:33:39.329 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 14:33:39.329 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:33:39.329 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 14:33:39.330 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:33:39.330 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 14:33:39.330 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:33:39.330 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:33:39.330 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 14:33:39.330 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:33:39.330 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 14:33:39.330 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:33:39.331 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 14:33:39.331 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:33:39.331 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:33:39.331 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 14:33:39.331 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:33:39.331 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:33:39.331 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 14:33:39.331 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:33:39.331 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:33:39.332 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 14:33:39.332 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:33:39.332 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 14:33:39.332 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:33:39.332 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 14:33:39.332 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:33:39.332 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 14:33:39.332 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:33:39.333 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 14:33:39.333 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 14:33:39.333 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:33:39.333 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 14:33:39.333 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 14:33:39.333 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 14:33:39.333 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 14:33:39.334 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 14:33:39.334 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 14:33:39.334 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:33:39.334 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 14:33:39.334 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:33:39.334 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 14:33:39.334 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:33:39.334 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 14:33:39.335 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:33:39.335 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 14:33:39.335 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:33:39.335 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 14:33:39.335 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:33:39.335 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 14:33:39.335 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:33:39.335 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 14:33:39.335 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:33:39.336 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 14:33:39.336 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 14:33:39.336 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 14:33:39.336 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:33:39.336 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:33:39.336 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:33:39.336 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:33:39.336 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:33:39.337 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:33:39.337 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 14:33:39.337 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:33:39.337 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 14:33:39.337 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:33:39.337 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 14:33:39.337 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 14:33:39.337 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 14:33:39.337 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:33:39.338 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 14:33:39.338 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:33:39.338 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 14:33:39.338 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:33:39.338 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 14:33:39.338 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:33:39.338 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 14:33:39.338 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:33:39.339 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 14:33:39.339 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:33:39.339 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 14:33:39.339 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:33:39.339 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 14:33:39.340 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:33:39.340 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 14:33:39.340 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:33:39.340 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 14:33:39.341 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:33:39.341 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:33:39.341 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:33:39.341 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:33:39.341 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:33:39.341 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:33:39.341 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:33:39.341 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:33:39.341 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:33:39.342 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:33:39.342 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:33:39.342 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:33:39.342 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:33:39.342 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:33:39.342 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:33:39.342 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:33:39.342 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:33:39.342 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:33:39.342 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:33:39.343 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:33:39.343 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:33:39.343 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:33:39.343 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 14:33:39.343 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:33:39.343 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 14:33:39.343 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:33:39.343 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 14:33:39.343 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 14:33:39.344 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 14:33:39.344 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=1, 剩余量=3
2025-08-20 14:33:39.344 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:33:39.344 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 14:33:39.344 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:33:39.344 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 14:33:39.344 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:33:39.344 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 14:33:39.344 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:33:39.344 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:33:39.344 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 14:33:39.345 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:33:39.345 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 14:33:39.345 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:33:39.345 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 14:33:39.345 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:33:39.345 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 14:33:39.345 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:33:39.345 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 14:33:39.345 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:33:39.345 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 14:33:39.346 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:33:39.346 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 14:33:39.346 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 14:33:39.346 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:33:39.346 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 14:33:39.346 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:33:39.346 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 14:33:39.346 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:33:39.346 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 14:33:39.346 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:33:39.346 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 14:33:39.347 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:33:39.347 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 14:33:39.347 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:33:39.347 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 14:33:39.347 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 14:33:39.347 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 14:33:39.347 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 14:33:39.347 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 14:33:39.348 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 14:33:39.348 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 14:33:39.348 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:33:39.348 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:33:39.348 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 14:33:39.349 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 14:33:39.349 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 14:33:39.349 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 14:33:39.349 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 14:33:39.349 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 14:33:39.350 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 14:33:39.350 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 14:33:39.350 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 14:33:39.350 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 14:33:39.350 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 14:33:39.351 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 14:33:39.351 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 14:36:43.514 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:43] - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 14:36:43.517 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:60] - 使用本地测试环境配置: localhost:9092
2025-08-20 14:36:43.564 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:89] - 应用本地测试Kafka配置
2025-08-20 14:36:43.619 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:255] - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 14:36:44.540 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:36:44.541 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:36:44.542 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:36:44.542 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:36:44.542 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:36:44.543 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:36:44.544 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:36:44.544 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:36:44.544 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:36:44.544 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:36:44.544 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:36:44.544 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:36:44.545 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:36:44.545 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:36:44.545 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:36:44.545 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:36:44.545 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:36:44.545 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:36:44.545 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:36:44.546 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:36:44.546 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:36:44.546 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:36:44.546 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:36:44.546 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:36:44.546 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:36:44.546 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:36:44.546 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:36:44.547 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:36:44.547 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:36:44.547 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:36:44.547 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:36:44.548 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:36:44.548 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:36:44.548 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:36:44.548 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 14:36:44.549 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 14:36:44.549 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:36:44.549 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 14:36:44.549 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:36:44.549 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 14:36:44.549 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:36:44.550 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 14:36:44.550 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 14:36:44.550 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:36:44.550 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:36:44.550 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 14:36:44.550 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:36:44.551 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:36:44.551 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 14:36:44.551 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:36:44.551 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:36:44.551 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:36:44.551 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 14:36:44.551 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:36:44.552 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:36:44.552 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:36:44.552 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:36:44.552 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 14:36:44.552 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:36:44.552 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:36:44.552 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:36:44.552 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 14:36:44.553 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:36:44.553 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 14:36:44.553 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:36:44.553 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 14:36:44.553 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 14:36:44.553 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 14:36:44.553 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 14:36:44.554 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 14:36:44.554 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 14:36:44.554 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 14:36:44.554 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 14:36:44.554 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 14:36:44.554 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 14:36:44.554 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 14:36:44.554 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:36:44.554 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:36:44.554 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 14:36:44.555 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:36:44.555 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:36:44.555 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:36:44.555 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:36:44.555 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 14:36:44.555 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:36:44.555 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:36:44.556 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 14:36:44.556 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:36:44.556 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:36:44.556 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:36:44.556 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:36:44.556 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 14:36:44.557 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:36:44.557 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:36:44.557 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:36:44.557 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 14:36:44.557 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:36:44.557 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:36:44.557 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 14:36:44.557 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:36:44.558 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:36:44.558 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 14:36:44.558 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:36:44.558 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:36:44.558 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 14:36:44.558 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 14:36:44.559 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 14:36:44.559 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:36:44.559 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:36:44.559 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 14:36:44.559 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:36:44.559 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:36:44.559 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 14:36:44.560 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:36:44.560 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 14:36:44.560 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:36:44.560 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 14:36:44.560 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 14:36:44.561 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 14:36:44.561 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:36:44.561 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:36:44.561 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:36:44.562 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 14:36:44.562 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:36:44.562 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 14:36:44.562 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:36:44.563 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:36:44.563 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 14:36:44.563 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:36:44.563 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 14:36:44.563 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:36:44.563 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 14:36:44.563 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:36:44.564 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 14:36:44.564 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:36:44.564 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:36:44.564 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 14:36:44.564 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:36:44.564 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:36:44.565 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 14:36:44.565 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:36:44.565 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 14:36:44.565 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:36:44.565 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 14:36:44.566 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:36:44.566 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 14:36:44.566 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 14:36:44.566 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:36:44.566 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 14:36:44.566 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:36:44.566 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:36:44.567 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:36:44.567 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:36:44.567 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:36:44.567 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:36:44.567 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:36:44.567 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:36:44.568 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:36:44.568 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:36:44.568 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:36:44.568 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:36:44.568 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:36:44.568 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:36:44.568 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:36:44.569 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:36:44.569 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:36:44.569 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 14:36:44.569 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 14:36:44.569 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:36:44.570 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 14:36:44.570 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 14:36:44.570 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=1, 剩余量=3
2025-08-20 14:36:44.570 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 14:36:44.570 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:36:44.570 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 14:36:44.570 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:36:44.571 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:36:44.571 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 14:36:44.571 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:36:44.571 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:36:44.571 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 14:36:44.572 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:36:44.572 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 14:36:44.572 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 14:36:44.572 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:36:44.572 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 14:36:44.572 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:36:44.572 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 14:36:44.573 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:36:44.573 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 14:36:44.573 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:36:44.573 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 14:36:44.573 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:36:44.573 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 14:36:44.573 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 14:36:44.573 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 14:36:44.574 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:36:44.574 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 14:36:44.574 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:36:44.574 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 14:36:44.575 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 14:36:44.575 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:36:44.575 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:36:44.576 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 14:36:44.576 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:36:44.576 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:36:44.576 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 14:36:44.576 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 14:36:44.576 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:36:44.576 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 14:36:44.577 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:36:44.577 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 14:36:44.577 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:36:44.577 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 14:36:44.577 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:36:44.577 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 14:36:44.577 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:36:44.577 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 14:36:44.577 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:36:44.577 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:36:44.577 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:36:44.578 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:36:44.578 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:36:44.578 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:36:44.578 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:36:44.578 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 14:36:44.578 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 14:36:44.578 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 14:36:44.579 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 14:36:44.579 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 14:36:44.579 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 14:36:44.579 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 14:36:44.579 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 14:36:44.580 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 14:36:44.580 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 14:36:44.580 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 14:36:44.580 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 14:36:44.580 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 14:36:44.581 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:36:44.581 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:36:44.581 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:36:44.581 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:36:44.581 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:36:44.582 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:36:44.582 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:36:44.582 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:36:44.582 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:36:44.582 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:39:22.430 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:43] - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 14:39:22.433 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:60] - 使用本地测试环境配置: localhost:9092
2025-08-20 14:39:22.478 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:89] - 应用本地测试Kafka配置
2025-08-20 14:39:22.531 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:255] - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 14:39:23.615 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:39:23.615 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:39:23.617 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:39:23.617 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:39:23.618 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:39:23.618 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:39:23.619 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:39:23.619 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:39:23.619 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:39:23.620 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:39:23.620 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:39:23.620 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:39:23.620 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:39:23.621 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:39:23.621 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:39:23.621 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:39:23.621 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:39:23.621 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:39:23.621 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:39:23.621 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:39:23.622 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:39:23.622 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:39:23.622 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:39:23.622 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:39:23.622 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:39:23.622 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:39:23.622 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:39:23.622 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:39:23.622 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:39:23.622 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:39:23.623 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:39:23.623 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:39:23.623 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:39:23.624 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:39:23.624 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:39:23.624 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:39:23.624 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:39:23.625 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:39:23.625 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 14:39:23.625 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 14:39:23.625 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:39:23.625 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 14:39:23.625 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 14:39:23.625 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 14:39:23.626 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 14:39:23.626 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:39:23.626 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 14:39:23.626 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:39:23.626 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 14:39:23.626 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 14:39:23.627 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 14:39:23.627 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:39:23.627 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:39:23.627 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:39:23.627 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:39:23.627 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 14:39:23.627 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:39:23.627 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 14:39:23.628 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 14:39:23.628 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:39:23.628 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 14:39:23.628 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 14:39:23.628 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:39:23.628 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:39:23.628 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:39:23.629 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 14:39:23.629 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:39:23.629 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:39:23.629 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:39:23.629 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 14:39:23.629 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:39:23.629 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:39:23.629 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 14:39:23.630 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:39:23.630 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:39:23.630 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 14:39:23.630 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 14:39:23.630 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:39:23.630 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 14:39:23.630 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:39:23.630 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 14:39:23.630 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 14:39:23.631 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:39:23.631 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 14:39:23.631 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:39:23.631 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 14:39:23.631 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 14:39:23.631 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:39:23.631 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:39:23.632 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:39:23.632 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:39:23.632 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:39:23.632 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 14:39:23.632 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:39:23.632 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 14:39:23.632 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:39:23.632 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:39:23.633 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 14:39:23.633 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:39:23.633 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:39:23.633 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:39:23.633 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 14:39:23.633 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:39:23.633 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 14:39:23.633 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:39:23.634 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 14:39:23.634 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:39:23.634 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:39:23.634 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 14:39:23.634 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:39:23.634 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:39:23.634 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 14:39:23.634 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:39:23.634 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 14:39:23.634 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:39:23.634 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 14:39:23.635 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:39:23.635 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:39:23.635 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 14:39:23.635 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:39:23.635 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 14:39:23.635 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:39:23.635 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 14:39:23.635 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:39:23.636 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:39:23.636 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:39:23.636 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:39:23.636 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:39:23.636 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:325] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:39:23.636 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:39:23.636 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:39:23.637 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 14:39:23.637 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:39:23.637 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 14:39:23.637 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:39:23.637 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:39:23.637 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 14:39:23.637 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:39:23.638 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 14:39:23.638 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:39:23.638 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 14:39:23.638 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:39:23.638 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 14:39:23.638 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:39:23.638 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 14:39:23.638 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:39:23.638 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 14:39:23.639 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:39:23.639 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 14:39:23.639 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 14:39:23.639 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:39:23.640 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 14:39:23.640 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:39:23.640 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 14:39:23.640 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:39:23.640 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 14:39:23.640 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:39:23.640 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:39:23.640 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:39:23.641 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:39:23.641 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:39:23.641 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:39:23.641 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:39:23.641 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:39:23.641 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:39:23.641 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:39:23.642 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:39:23.642 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 14:39:23.642 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:39:23.642 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 14:39:23.642 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:39:23.642 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:39:23.642 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:39:23.643 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:39:23.643 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:39:23.643 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:39:23.643 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:39:23.643 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:39:23.643 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:39:23.643 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 14:39:23.643 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:39:23.644 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 14:39:23.644 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:39:23.644 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 14:39:23.644 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=1, 剩余量=3
2025-08-20 14:39:23.644 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:39:23.644 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:39:23.645 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:39:23.645 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 14:39:23.645 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:39:23.645 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 14:39:23.645 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 14:39:23.645 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 14:39:23.645 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:39:23.646 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:39:23.646 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 14:39:23.646 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:39:23.646 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 14:39:23.646 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 14:39:23.646 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:39:23.646 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 14:39:23.646 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:39:23.647 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 14:39:23.647 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:39:23.647 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 14:39:23.647 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 14:39:23.647 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:39:23.647 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 14:39:23.647 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:39:23.647 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 14:39:23.647 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:39:23.647 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 14:39:23.648 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:39:23.648 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 14:39:23.648 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:39:23.648 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:39:23.648 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 14:39:23.648 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:39:23.648 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 14:39:23.648 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:39:23.649 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 14:39:23.649 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:39:23.649 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 14:39:23.649 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:39:23.649 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 14:39:23.649 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 14:39:23.649 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:39:23.650 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 14:39:23.650 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 14:39:23.650 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:39:23.650 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:39:23.650 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:39:23.650 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:39:23.650 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:39:23.651 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 14:39:23.651 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:338] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:39:23.651 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 14:39:23.651 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 14:39:23.651 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 14:39:23.652 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 14:39:23.652 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 14:39:23.652 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 14:39:23.652 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 14:39:23.652 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 14:39:23.653 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 14:39:23.653 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 14:39:23.653 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 14:39:23.653 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 14:39:23.654 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:39:23.654 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:39:23.654 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:39:23.654 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:39:23.655 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:39:23.655 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:39:23.655 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:39:23.656 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:39:23.656 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:39:23.656 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:39:23.657 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:39:23.657 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:39:23.657 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:39:23.657 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:39:23.657 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:46:26.579 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:43] - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 14:46:26.581 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:60] - 使用本地测试环境配置: localhost:9092
2025-08-20 14:46:26.620 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:89] - 应用本地测试Kafka配置
2025-08-20 14:46:26.678 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:255] - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 14:46:27.601 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:46:27.601 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:46:27.603 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:46:27.603 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:46:27.603 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:46:27.604 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:46:27.604 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:46:27.604 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:46:27.605 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:46:27.605 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:46:27.605 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:46:27.605 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:46:27.605 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:46:27.605 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:46:27.605 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:46:27.606 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:46:27.606 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:46:27.606 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:46:27.606 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:46:27.606 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:46:27.606 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:46:27.606 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:46:27.607 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:46:27.607 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:46:27.607 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:46:27.607 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:46:27.607 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:46:27.607 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:46:27.608 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:46:27.608 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:46:27.608 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:46:27.608 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:46:27.608 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 14:46:27.608 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:46:27.608 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 14:46:27.608 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:46:27.609 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 14:46:27.609 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:46:27.609 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 14:46:27.609 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 14:46:27.609 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:46:27.609 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 14:46:27.610 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:46:27.610 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 14:46:27.610 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:46:27.610 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:46:27.610 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:46:27.610 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:46:27.610 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 14:46:27.611 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:46:27.611 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:46:27.611 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 14:46:27.611 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:46:27.611 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 14:46:27.611 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:46:27.611 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:46:27.611 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 14:46:27.611 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:46:27.612 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 14:46:27.612 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:46:27.612 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:46:27.612 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 14:46:27.612 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:46:27.612 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:46:27.612 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 14:46:27.613 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 14:46:27.613 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:46:27.613 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 14:46:27.613 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 14:46:27.613 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 14:46:27.613 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:46:27.613 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 14:46:27.614 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 14:46:27.614 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:46:27.614 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 14:46:27.614 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 14:46:27.614 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:46:27.614 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 14:46:27.614 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 14:46:27.615 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 14:46:27.615 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:46:27.615 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 14:46:27.615 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 14:46:27.615 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 14:46:27.615 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 14:46:27.615 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:46:27.615 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 14:46:27.616 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 14:46:27.616 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:46:27.616 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:46:27.616 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 14:46:27.616 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:46:27.616 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:46:27.616 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:46:27.617 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:46:27.617 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 14:46:27.617 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:46:27.617 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:46:27.617 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:46:27.617 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 14:46:27.617 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:46:27.617 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:46:27.617 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:46:27.618 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 14:46:27.618 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:46:27.618 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 14:46:27.618 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 14:46:27.618 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:46:27.618 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:46:27.618 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 14:46:27.618 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:46:27.618 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:46:27.618 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:46:27.619 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:46:27.619 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:46:27.619 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:46:27.619 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:46:27.619 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 14:46:27.619 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:46:27.619 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:46:27.619 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 14:46:27.619 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 14:46:27.620 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 14:46:27.620 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:46:27.620 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:46:27.620 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:46:27.620 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:46:27.620 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 14:46:27.620 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:46:27.620 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:46:27.620 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:46:27.621 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:46:27.621 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:46:27.621 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:46:27.621 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 14:46:27.621 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:46:27.621 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:46:27.621 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:46:27.621 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 14:46:27.621 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:46:27.622 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:46:27.622 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 14:46:27.622 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:46:27.622 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:46:27.622 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 14:46:27.623 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:46:27.623 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:46:27.623 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 14:46:27.623 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:46:27.623 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:46:27.623 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 14:46:27.623 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:46:27.624 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:46:27.624 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 14:46:27.624 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:46:27.624 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 14:46:27.624 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:46:27.624 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 14:46:27.624 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:46:27.624 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:46:27.624 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:46:27.624 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:46:27.625 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:46:27.625 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:46:27.625 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:46:27.625 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 14:46:27.625 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:46:27.625 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 14:46:27.625 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:46:27.625 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:46:27.625 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:46:27.625 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:46:27.626 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:46:27.626 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:46:27.626 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:46:27.626 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:46:27.626 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:46:27.626 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:46:27.626 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:46:27.626 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:46:27.627 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:46:27.627 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:46:27.627 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:46:27.627 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 14:46:27.627 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:46:27.627 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 14:46:27.627 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:46:27.627 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 14:46:27.627 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 14:46:27.628 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:46:27.628 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=1, 剩余量=3
2025-08-20 14:46:27.628 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 14:46:27.628 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 14:46:27.628 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 14:46:27.628 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:46:27.628 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 14:46:27.629 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 14:46:27.629 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:46:27.629 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 14:46:27.629 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:46:27.629 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 14:46:27.629 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:46:27.629 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:46:27.630 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 14:46:27.630 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 14:46:27.630 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:46:27.630 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 14:46:27.630 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:46:27.630 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 14:46:27.631 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 14:46:27.631 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:46:27.631 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 14:46:27.631 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:46:27.631 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 14:46:27.631 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:46:27.631 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 14:46:27.631 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:46:27.631 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 14:46:27.632 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:46:27.632 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 14:46:27.632 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:46:27.632 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 14:46:27.632 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:46:27.632 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 14:46:27.632 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:46:27.632 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 14:46:27.632 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:46:27.632 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:46:27.632 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 14:46:27.633 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:46:27.633 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 14:46:27.633 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:46:27.633 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:46:27.633 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:46:27.633 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:46:27.633 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 14:46:27.633 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 14:46:27.634 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 14:46:27.634 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 14:46:27.634 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 14:46:27.634 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 14:46:27.635 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 14:46:27.635 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 14:46:27.635 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 14:46:27.635 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 14:46:27.635 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 14:46:27.635 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 14:46:27.636 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 14:46:27.636 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:46:27.636 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:46:27.636 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:46:27.636 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:46:27.637 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:46:27.637 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:46:27.637 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:46:27.637 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:46:27.638 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:46:27.638 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:46:27.638 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:46:27.638 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:46:27.638 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:46:27.639 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:46:27.639 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:47:03.368 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:47:03.485 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:47:03.603 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:47:03.717 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:47:03.836 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:47:03.953 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:47:04.066 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:47:04.183 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:47:04.303 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:47:04.423 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:47:04.539 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 14:47:04.659 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 14:47:04.773 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 14:47:04.892 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=1, 剩余量=3
2025-08-20 14:47:05.008 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 14:47:05.126 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 14:47:05.242 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 14:47:05.359 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 14:47:05.473 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 14:47:05.593 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 14:47:05.707 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 14:47:05.822 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 14:47:05.937 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 14:47:06.053 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 14:47:06.171 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 14:47:06.287 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 14:47:06.411 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 14:47:06.522 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 14:47:06.637 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 14:47:06.753 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 14:47:06.870 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 14:47:06.987 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 14:47:07.103 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 14:47:07.221 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 14:47:07.341 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 14:47:07.460 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:47:07.575 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:47:07.696 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 14:47:07.809 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 14:47:07.929 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 14:47:08.045 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 14:47:08.160 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 14:47:08.277 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 14:47:08.395 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 14:47:08.512 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 14:47:08.633 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 14:47:08.745 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 14:47:08.859 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 14:47:08.976 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 14:47:09.093 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 14:47:10.219 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:47:10.338 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:47:10.455 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:47:10.571 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:47:10.689 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:47:10.804 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:47:10.922 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:47:11.040 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:47:11.160 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:47:11.275 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:47:11.392 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 14:47:11.506 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 14:47:11.625 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 14:47:11.744 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 14:47:11.858 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 14:47:12.985 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:47:13.103 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:47:13.218 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:47:13.336 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:47:13.455 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:47:13.573 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:47:13.689 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:47:13.807 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:47:13.921 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:47:14.040 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:47:14.156 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:14.275 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:14.389 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:14.511 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:14.626 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:14.739 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:47:14.855 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:14.974 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:47:15.091 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:15.211 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:15.329 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:15.447 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:15.561 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:47:15.677 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 14:47:15.793 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 14:47:15.913 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:47:16.032 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:16.149 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:16.265 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:16.384 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:16.501 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:16.617 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:16.732 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:16.849 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:16.963 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:17.079 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:17.194 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 14:47:17.315 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:17.430 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:17.548 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:17.664 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:17.781 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:17.897 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:47:18.013 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 14:47:43.946 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:43] - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 14:47:43.948 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:60] - 使用本地测试环境配置: localhost:9092
2025-08-20 14:47:43.990 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:89] - 应用本地测试Kafka配置
2025-08-20 14:47:44.042 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:255] - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 14:47:44.950 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:47:44.950 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:47:44.951 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:47:44.952 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:47:44.952 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:47:44.952 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:47:44.953 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:47:44.953 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:47:44.953 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:47:44.954 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:47:44.954 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:47:44.954 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:47:44.955 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.955 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.955 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.956 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.956 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.956 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:47:44.956 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.957 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:47:44.957 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.957 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.957 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:47:44.958 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:44.958 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:47:44.958 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.958 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:47:44.958 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:47:44.958 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 14:47:44.958 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:47:44.959 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:47:44.959 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:47:44.959 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 14:47:44.959 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:47:44.959 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:47:44.959 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:47:44.959 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:47:44.960 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:47:44.960 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:47:44.960 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.960 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:47:44.960 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:47:44.960 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.960 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:47:44.961 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:47:44.961 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.961 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:47:44.961 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 14:47:44.961 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.961 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:47:44.961 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 14:47:44.962 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:47:44.962 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 14:47:44.962 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:44.962 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 14:47:44.962 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 14:47:44.962 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 14:47:44.962 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 14:47:44.962 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.962 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 14:47:44.962 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 14:47:44.963 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 14:47:44.963 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 14:47:44.963 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.963 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 14:47:44.963 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 14:47:44.963 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:47:44.963 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 14:47:44.963 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.963 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:47:44.964 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 14:47:44.964 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:47:44.964 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 14:47:44.964 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:47:44.964 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:44.964 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 14:47:44.964 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:47:44.964 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 14:47:44.964 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:47:44.964 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.965 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:47:44.965 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 14:47:44.965 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 14:47:44.965 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:47:44.965 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 14:47:44.965 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:47:44.965 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 14:47:44.965 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.966 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 14:47:44.966 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:47:44.966 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 14:47:44.966 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.966 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 14:47:44.966 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 14:47:44.966 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.967 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 14:47:44.967 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 14:47:44.967 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 14:47:44.967 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:44.967 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 14:47:44.967 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 14:47:44.967 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 14:47:44.967 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.968 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 14:47:44.968 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:47:44.968 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 14:47:44.968 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 14:47:44.968 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 14:47:44.969 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:47:44.969 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 14:47:44.969 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:47:44.969 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:47:44.969 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:47:44.969 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:47:44.969 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:47:44.969 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:47:44.970 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:47:44.970 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:47:44.970 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 14:47:44.970 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:47:44.970 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 14:47:44.970 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:47:44.970 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 14:47:44.971 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:47:44.971 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:47:44.971 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 14:47:44.971 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:47:44.971 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:47:44.971 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 14:47:44.971 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:47:44.971 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 14:47:44.972 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:47:44.972 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 14:47:44.972 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.972 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:47:44.972 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 14:47:44.972 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.972 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:47:44.973 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.973 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 14:47:44.973 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:47:44.973 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.973 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.973 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 14:47:44.973 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:47:44.973 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:47:44.974 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 14:47:44.974 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.974 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:47:44.974 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 14:47:44.974 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:47:44.974 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:47:44.974 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 14:47:44.974 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.974 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.974 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:47:44.974 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:47:44.975 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:44.975 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:47:44.975 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:47:44.975 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.975 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:47:44.975 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:47:44.975 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:47:44.975 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:47:44.975 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:47:44.975 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 14:47:44.975 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 14:47:44.975 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:47:44.975 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 14:47:44.976 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 14:47:44.976 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:47:44.976 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:47:44.976 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 14:47:44.976 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.976 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:47:44.976 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 14:47:44.976 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.976 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:47:44.976 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 14:47:44.976 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.976 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:47:44.976 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.977 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 14:47:44.977 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:47:44.977 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:44.977 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 14:47:44.977 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 14:47:44.977 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.977 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 14:47:44.977 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 14:47:44.977 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.977 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 14:47:44.977 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 14:47:44.977 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.978 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=1, 剩余量=3
2025-08-20 14:47:44.978 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:44.978 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 14:47:44.978 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 14:47:44.978 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.978 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 14:47:44.978 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 14:47:44.978 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 14:47:44.978 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 14:47:44.979 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 14:47:44.979 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 14:47:44.979 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 14:47:44.979 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 14:47:44.979 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.979 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 14:47:44.979 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 14:47:44.979 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 14:47:44.979 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 14:47:44.979 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.980 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 14:47:44.980 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.980 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 14:47:44.980 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:44.980 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 14:47:44.980 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.980 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 14:47:44.980 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:47:44.980 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 14:47:44.980 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 14:47:44.981 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 14:47:44.981 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:47:44.981 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:47:44.981 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 14:47:44.981 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:47:44.981 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:47:44.982 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:47:44.982 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 14:47:44.982 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:47:44.982 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:47:44.982 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 14:47:44.982 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:47:44.982 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:47:44.983 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 14:47:44.983 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:47:44.983 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:47:44.983 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:47:44.983 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 14:47:44.984 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:47:44.984 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 14:47:44.984 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 14:47:44.984 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:47:44.984 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 14:47:44.984 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:47:44.984 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:47:44.985 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:47:44.985 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:47:44.985 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:47:44.985 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 14:47:44.985 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 14:47:44.985 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 14:47:44.985 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 14:47:44.985 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 14:47:44.986 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 14:47:44.986 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:47:44.986 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 14:47:44.986 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 14:47:44.986 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 14:47:44.986 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 14:47:44.986 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 14:47:44.987 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 14:47:44.987 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 14:47:44.987 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 14:47:44.987 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.987 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.987 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 14:47:44.987 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.987 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 14:47:44.987 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.988 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 14:47:44.988 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.988 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 14:47:44.988 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:47:44.988 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 14:47:44.988 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 14:47:44.988 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:47:44.988 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 14:47:44.988 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.988 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:47:44.988 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.989 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:47:44.989 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:44.989 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:47:44.989 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.989 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:47:44.989 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:47:44.989 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:47:44.989 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 14:47:44.989 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:47:44.989 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:47:44.989 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 14:47:44.990 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:47:44.990 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:47:44.990 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 14:47:44.990 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:47:44.990 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.990 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:47:44.991 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:47:44.991 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.991 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:47:44.991 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.991 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:47:44.991 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.991 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:47:44.991 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:44.991 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 14:47:44.991 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.991 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 14:47:44.992 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.992 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 14:47:44.992 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.992 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 14:47:44.992 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:44.992 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 14:47:44.992 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.992 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 14:47:44.992 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 14:47:44.992 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 14:47:44.993 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 14:47:44.993 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 14:47:44.993 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.993 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 14:47:44.993 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 14:47:44.993 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 14:47:44.993 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 14:47:44.993 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 14:47:44.993 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 14:47:44.994 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 14:47:44.994 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 14:47:44.994 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=1, 剩余量=3
2025-08-20 14:47:44.994 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 14:47:44.994 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 14:47:44.994 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 14:47:44.994 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 14:47:44.994 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 14:47:44.994 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 14:47:44.995 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 14:47:44.995 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 14:47:44.995 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 14:47:44.995 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 14:47:44.996 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 14:47:44.996 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 14:47:44.996 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 14:47:44.996 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 14:47:44.996 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 14:47:44.996 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 14:47:44.997 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 14:47:44.997 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 14:47:44.998 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 14:47:44.998 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 14:47:44.998 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 14:47:44.998 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 14:47:44.999 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 14:47:44.999 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:47:44.999 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 14:47:44.999 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 14:47:44.999 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 14:47:44.999 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 14:47:45.000 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 14:47:45.000 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 14:47:45.000 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 14:47:45.000 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 14:47:45.000 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 14:47:45.000 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 14:47:45.001 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 14:47:45.001 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 14:47:45.001 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 14:47:45.001 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 15:15:03.271 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:43] - 启动期货全量订单簿重建系统(Kafka版)...
2025-08-20 15:15:03.273 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:60] - 使用本地测试环境配置: localhost:9092
2025-08-20 15:15:03.315 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:89] - 应用本地测试Kafka配置
2025-08-20 15:15:03.363 [main] INFO  [c.f.j.FuturesOrderBookKafkaJob:255] - 开始执行Flink作业(Kafka版 - local 环境)...
2025-08-20 15:15:04.281 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 15:15:04.281 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 15:15:04.282 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 15:15:04.282 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 15:15:04.283 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 15:15:04.283 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 15:15:04.283 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 15:15:04.284 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 15:15:04.284 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 15:15:04.285 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 15:15:04.285 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 15:15:04.285 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 15:15:04.285 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 15:15:04.285 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 15:15:04.285 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 15:15:04.286 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 15:15:04.286 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 15:15:04.286 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 15:15:04.286 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 15:15:04.286 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 15:15:04.286 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.286 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 15:15:04.287 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 15:15:04.287 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.287 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 15:15:04.287 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.287 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 15:15:04.287 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 15:15:04.287 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.287 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 15:15:04.287 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 15:15:04.287 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.288 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 15:15:04.288 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 15:15:04.288 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 15:15:04.288 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 15:15:04.288 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 15:15:04.288 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 15:15:04.288 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 15:15:04.288 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.289 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 15:15:04.289 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 15:15:04.289 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 15:15:04.289 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 15:15:04.289 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 15:15:04.289 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 15:15:04.289 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 15:15:04.289 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.289 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 15:15:04.290 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 15:15:04.290 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 15:15:04.290 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.290 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 15:15:04.290 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 15:15:04.290 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 15:15:04.290 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.290 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 15:15:04.290 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 15:15:04.291 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 15:15:04.291 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 15:15:04.291 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 15:15:04.291 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 15:15:04.291 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 15:15:04.291 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 15:15:04.291 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 15:15:04.291 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 15:15:04.292 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 15:15:04.292 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 15:15:04.292 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 15:15:04.292 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 15:15:04.292 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.292 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 15:15:04.292 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 15:15:04.292 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.292 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 15:15:04.293 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.293 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 15:15:04.293 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 15:15:04.293 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.293 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 15:15:04.293 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 15:15:04.293 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 15:15:04.293 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 15:15:04.293 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 15:15:04.293 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 15:15:04.294 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 15:15:04.294 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.294 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 15:15:04.294 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 15:15:04.294 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 15:15:04.294 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 15:15:04.294 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.294 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 15:15:04.294 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 15:15:04.294 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 15:15:04.295 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.295 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 15:15:04.295 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 15:15:04.295 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 15:15:04.295 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 15:15:04.295 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 15:15:04.295 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 15:15:04.295 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.295 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 15:15:04.296 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 15:15:04.296 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 15:15:04.296 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 15:15:04.296 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 15:15:04.296 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 15:15:04.296 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 15:15:04.296 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.296 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 15:15:04.296 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 15:15:04.297 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 15:15:04.297 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 15:15:04.297 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.297 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 15:15:04.297 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 15:15:04.297 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.297 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 15:15:04.297 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 15:15:04.297 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 15:15:04.297 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 15:15:04.297 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 15:15:04.298 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 15:15:04.298 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 15:15:04.298 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.298 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 15:15:04.298 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 15:15:04.298 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 15:15:04.298 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 15:15:04.298 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=3, 剩余量=13
2025-08-20 15:15:04.298 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 15:15:04.299 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 15:15:04.299 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=12
2025-08-20 15:15:04.299 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 15:15:04.299 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 15:15:04.299 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=11
2025-08-20 15:15:04.299 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 15:15:04.299 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 15:15:04.299 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=10
2025-08-20 15:15:04.299 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 15:15:04.299 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=9
2025-08-20 15:15:04.299 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 15:15:04.299 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 15:15:04.300 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 15:15:04.300 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=8
2025-08-20 15:15:04.300 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 15:15:04.300 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=7
2025-08-20 15:15:04.300 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 15:15:04.300 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 15:15:04.300 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=6
2025-08-20 15:15:04.300 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 15:15:04.300 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 15:15:04.300 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=4
2025-08-20 15:15:04.300 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 15:15:04.300 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 15:15:04.300 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=2
2025-08-20 15:15:04.301 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 15:15:04.301 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 15:15:04.301 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=1, 剩余量=1
2025-08-20 15:15:04.301 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 15:15:04.301 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 15:15:04.301 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257414, 状态=0, 剩余量=1
2025-08-20 15:15:04.301 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.301 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 15:15:04.301 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=4
2025-08-20 15:15:04.301 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.301 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 15:15:04.301 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257401, 状态=3, 剩余量=2
2025-08-20 15:15:04.301 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.302 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 15:15:04.302 [Source: Kafka-组合订单 -> Process -> Map -> 解析组合订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:314] - 组合订单解析: 合约=SPD_EB2504_EB2505, 订单号=100257400, 状态=3, 剩余量=2
2025-08-20 15:15:04.302 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.302 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 15:15:04.302 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.302 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 15:15:04.302 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 15:15:04.302 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 15:15:04.302 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.302 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 15:15:04.303 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 15:15:04.303 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 15:15:04.303 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.303 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 15:15:04.303 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.303 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 15:15:04.303 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 15:15:04.303 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=1, 剩余量=3
2025-08-20 15:15:04.303 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.304 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 15:15:04.304 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 15:15:04.304 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 15:15:04.304 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 15:15:04.304 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 15:15:04.304 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 15:15:04.304 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 15:15:04.305 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 15:15:04.305 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 15:15:04.305 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 15:15:04.305 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 15:15:04.305 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.305 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 15:15:04.305 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.305 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 15:15:04.306 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 15:15:04.306 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.306 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 15:15:04.306 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.306 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 15:15:04.306 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 15:15:04.306 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 15:15:04.307 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 15:15:04.307 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.307 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 15:15:04.307 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.307 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.307 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 15:15:04.307 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 15:15:04.308 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 15:15:04.308 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.308 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 15:15:04.308 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 15:15:04.308 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.308 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.308 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 15:15:04.308 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.309 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 15:15:04.309 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 15:15:04.309 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 15:15:04.309 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.309 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 15:15:04.309 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 15:15:04.309 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 15:15:04.309 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 15:15:04.310 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 15:15:04.310 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 15:15:04.310 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 15:15:04.310 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 15:15:04.310 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 15:15:04.310 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 15:15:04.310 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 15:15:04.310 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 15:15:04.310 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 15:15:04.311 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 15:15:04.311 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 15:15:04.311 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 15:15:04.311 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 15:15:04.311 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 15:15:04.311 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 15:15:04.311 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 15:15:04.311 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 15:15:04.311 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 15:15:04.311 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 15:15:04.312 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 15:15:04.312 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 15:15:04.312 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 15:15:04.312 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 15:15:04.312 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 15:15:04.312 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 15:15:04.313 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 15:15:04.313 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 15:15:04.313 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 15:15:04.313 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355625, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 15:15:04.313 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 15:15:04.313 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=184, 方向=B, 成交量=5, 成交价=4.5, 开平=0, 类型=0
2025-08-20 15:15:04.313 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 15:15:04.313 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=JD2504-C-3200, 交易号=101355626, 会员=148, 方向=S, 成交量=5, 成交价=4.5, 开平=1, 类型=0
2025-08-20 15:15:04.313 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 15:15:04.313 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=173, 方向=B, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 15:15:04.313 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 15:15:04.314 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=CS2505-C-2750, 交易号=101355694, 会员=110, 方向=S, 成交量=1, 成交价=13.0, 开平=0, 类型=0
2025-08-20 15:15:04.314 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 15:15:04.314 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=151, 方向=B, 成交量=10, 成交价=8035.0, 开平=0, 类型=0
2025-08-20 15:15:04.314 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 15:15:04.314 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=101355700, 会员=101, 方向=B, 成交量=10, 成交价=8035.0, 开平=1, 类型=0
2025-08-20 15:15:04.314 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 15:15:04.314 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=109, 方向=B, 成交量=1, 成交价=2690.0, 开平=0, 类型=0
2025-08-20 15:15:04.314 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 15:15:04.314 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=SH2505, 交易号=48060, 会员=7, 方向=S, 成交量=1, 成交价=2690.0, 开平=1, 类型=0
2025-08-20 15:15:04.314 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 15:15:04.314 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=122, 方向=B, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.315 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 15:15:04.315 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052597, 会员=97, 方向=S, 成交量=8, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.315 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 15:15:04.315 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.315 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 15:15:04.315 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 15:15:04.315 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052678, 会员=125, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.316 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 15:15:04.316 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.316 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 15:15:04.316 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052768, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 15:15:04.316 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 15:15:04.316 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=122, 方向=B, 成交量=1, 成交价=8037.0, 开平=1, 类型=0
2025-08-20 15:15:04.316 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 15:15:04.316 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052770, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 15:15:04.316 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 15:15:04.316 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=122, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.316 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 15:15:04.316 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052795, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.317 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 15:15:04.317 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 15:15:04.317 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 15:15:04.317 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052796, 会员=149, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.317 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 15:15:04.317 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 15:15:04.317 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=184, 方向=S, 成交量=1, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 15:15:04.317 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 15:15:04.317 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 15:15:04.318 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052799, 会员=151, 方向=B, 成交量=1, 成交价=8095.0, 开平=0, 类型=2
2025-08-20 15:15:04.318 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 15:15:04.318 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=11, 方向=B, 成交量=1, 成交价=8037.0, 开平=0, 类型=0
2025-08-20 15:15:04.318 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 15:15:04.318 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 15:15:04.318 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052800, 会员=151, 方向=S, 成交量=1, 成交价=8037.0, 开平=1, 类型=2
2025-08-20 15:15:04.318 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=21, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.318 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 15:15:04.318 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=1, 剩余量=3
2025-08-20 15:15:04.318 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052821, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.319 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257097, 状态=3, 剩余量=10
2025-08-20 15:15:04.319 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.319 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 15:15:04.319 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052822, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.319 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=0, 剩余量=0
2025-08-20 15:15:04.319 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=151, 方向=B, 成交量=3, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 15:15:04.319 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 15:15:04.319 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052823, 会员=153, 方向=S, 成交量=3, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.319 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=0, 剩余量=0
2025-08-20 15:15:04.319 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=22, 方向=B, 成交量=2, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.319 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257098, 状态=3, 剩余量=3
2025-08-20 15:15:04.319 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052833, 会员=151, 方向=S, 成交量=2, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.320 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 15:15:04.320 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=151, 方向=B, 成交量=2, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 15:15:04.320 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 15:15:04.320 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052834, 会员=110, 方向=S, 成交量=2, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.320 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 15:15:04.320 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=B, 成交量=1, 成交价=8094.0, 开平=1, 类型=0
2025-08-20 15:15:04.320 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 15:15:04.320 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052846, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.320 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 15:15:04.320 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=22, 方向=B, 成交量=1, 成交价=8036.0, 开平=1, 类型=0
2025-08-20 15:15:04.320 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 15:15:04.320 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2505, 交易号=100052847, 会员=151, 方向=S, 成交量=1, 成交价=8036.0, 开平=1, 类型=2
2025-08-20 15:15:04.321 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 15:15:04.321 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=151, 方向=B, 成交量=1, 成交价=8094.0, 开平=0, 类型=2
2025-08-20 15:15:04.321 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 15:15:04.321 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052848, 会员=49, 方向=S, 成交量=1, 成交价=8094.0, 开平=0, 类型=0
2025-08-20 15:15:04.321 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257009, 状态=3, 剩余量=1
2025-08-20 15:15:04.321 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=151, 方向=B, 成交量=10, 成交价=8095.0, 开平=0, 类型=0
2025-08-20 15:15:04.321 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 15:15:04.321 [Source: Kafka-交易 -> Process -> Map -> 解析交易事件 (1/1)#0] INFO  [TRADE_PROCESSING:327] - 交易解析: 合约=EB2504, 交易号=100052849, 会员=101, 方向=S, 成交量=10, 成交价=8095.0, 开平=1, 类型=0
2025-08-20 15:15:04.321 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 15:15:04.321 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257008, 状态=3, 剩余量=1
2025-08-20 15:15:04.321 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 15:15:04.322 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
2025-08-20 15:15:04.322 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=0, 剩余量=0
2025-08-20 15:15:04.322 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 15:15:04.322 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=1, 剩余量=1
2025-08-20 15:15:04.322 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=3, 剩余量=1
2025-08-20 15:15:04.322 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257259, 状态=0, 剩余量=0
2025-08-20 15:15:04.323 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=0, 剩余量=0
2025-08-20 15:15:04.323 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=3, 剩余量=1
2025-08-20 15:15:04.323 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100254367, 状态=0, 剩余量=0
2025-08-20 15:15:04.323 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=3, 剩余量=1
2025-08-20 15:15:04.323 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257479, 状态=0, 剩余量=0
2025-08-20 15:15:04.323 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=3, 剩余量=1
2025-08-20 15:15:04.324 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100256192, 状态=0, 剩余量=0
2025-08-20 15:15:04.324 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=3, 剩余量=1
2025-08-20 15:15:04.324 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257507, 状态=0, 剩余量=0
2025-08-20 15:15:04.324 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=3, 剩余量=1
2025-08-20 15:15:04.324 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257466, 状态=0, 剩余量=0
2025-08-20 15:17:04.371 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=3, 剩余量=5
2025-08-20 15:17:04.480 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=3, 剩余量=5
2025-08-20 15:17:04.594 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000001, 状态=0, 剩余量=0
2025-08-20 15:17:04.713 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=3, 剩余量=5
2025-08-20 15:17:04.829 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000005, 状态=0, 剩余量=0
2025-08-20 15:17:04.947 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=3, 剩余量=5
2025-08-20 15:17:05.063 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000009, 状态=0, 剩余量=0
2025-08-20 15:17:05.178 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=3, 剩余量=5
2025-08-20 15:17:05.298 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000011, 状态=0, 剩余量=0
2025-08-20 15:17:05.420 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=3, 剩余量=1
2025-08-20 15:17:05.535 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000017, 状态=0, 剩余量=0
2025-08-20 15:17:05.651 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=3, 剩余量=1
2025-08-20 15:17:05.766 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=CS2505-C-2750, 订单号=10000019, 状态=0, 剩余量=0
2025-08-20 15:17:05.881 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=JD2504-C-3200, 订单号=10000000, 状态=5, 剩余量=5
2025-08-20 15:17:06.001 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=10
2025-08-20 15:17:06.119 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=1, 剩余量=0
2025-08-20 15:17:06.233 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=10
2025-08-20 15:17:06.353 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=0
2025-08-20 15:17:06.471 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=3, 剩余量=1
2025-08-20 15:17:06.590 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000021, 状态=0, 剩余量=0
2025-08-20 15:17:06.705 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=3, 剩余量=1
2025-08-20 15:17:06.825 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=SH2505, 订单号=10000023, 状态=0, 剩余量=0
2025-08-20 15:17:06.946 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257000, 状态=3, 剩余量=1
2025-08-20 15:17:07.063 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257001, 状态=3, 剩余量=1
2025-08-20 15:17:07.179 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257002, 状态=3, 剩余量=5
2025-08-20 15:17:07.294 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2504, 订单号=100257003, 状态=3, 剩余量=1
2025-08-20 15:17:07.413 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257004, 状态=3, 剩余量=1
2025-08-20 15:17:07.526 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257005, 状态=3, 剩余量=1
2025-08-20 15:17:07.644 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257006, 状态=3, 剩余量=5
2025-08-20 15:17:07.762 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257007, 状态=3, 剩余量=1
2025-08-20 15:17:07.880 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100256966, 状态=3, 剩余量=9
2025-08-20 15:17:07.993 [Source: Kafka-单腿订单 -> Process -> Map -> 解析单腿订单 -> Map (1/1)#0] DEBUG [TRADE_PROCESSING:287] - 单腿订单解析: 合约=EB2505, 订单号=100257085, 状态=3, 剩余量=8
